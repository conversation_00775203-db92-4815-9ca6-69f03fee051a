## Related Tickets
- [#Task xxxxx: Content](https://edu-redmine.sun-asterisk.vn/issues/xxxx)
## WHAT
- 
## Evidence (Screenshot or Video)

## Review Checklist

Category | View Point | Description | Self review | Reviewer2 (name)
--- | --- | --- | --- | ---
Conventions | Sun* coding conventions | https://sal.vn/WtYPAy |<li>- [ ] yes</li>|<li>- [ ] yes</li>  
Conventions | Kotlin coding conventions | https://kotlinlang.org/docs/coding-conventions.html |<li>- [ ] yes</li>|<li>- [ ] yes</li>  
Redmine | Sun* Redmine working process  | https://sal.vn/piPoQn |<li>- [ ] yes</li>|<li>- [ ] yes</li>  

## Notes (Optional)
*(Impacted Areas in Application(List features, api, models or services that this PR will affect))*

*(List gem, library third party add new)*

*(Other notes)*
